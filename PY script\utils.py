# utils.py

import os
import csv
import logging
import time
from datetime import datetime, timedelta
import requests
import pandas as pd
import pytz
import sys

import config

logger = logging.getLogger(__name__)

def setup_logging():
    log_level_str = getattr(config, 'LOG_LEVEL', 'INFO').upper()
    level = getattr(logging, log_level_str, logging.INFO)
    
    root_logger = logging.getLogger()
    if root_logger.hasHandlers():
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            
    logging.basicConfig(
        level=level,
        format=config.LOG_FORMAT,
        datefmt=config.LOG_DATE_FORMAT,
        handlers=[logging.StreamHandler(sys.stdout)] 
    )
    logging.getLogger("ib_insync.client").setLevel(logging.INFO) 
    logging.getLogger("urllib3.connectionpool").setLevel(logging.INFO)
    logger.info(f"Logging setup complete. Level: {log_level_str}")


def append_trade_to_csv(symbol, signal, entry_price, exit_price, pnl, entry_time_bot, sl_level, trail_activated):
    try:
        header = ['TimestampUTC', 'Symbol', 'Signal', 'EntryPrice', 'ExitPrice', 'PnL_USD', 'EntryTime_Bot', 'SLLevel', 'TrailActivated']
        trades_csv_path = getattr(config, 'TRADES_CSV_FILE', 'trades_log.csv') 
        
        timestamp_utc_str = datetime.now(pytz.utc).strftime('%Y-%m-%d %H:%M:%S %Z')
        
        row_data = [
            timestamp_utc_str,
            str(symbol) if symbol is not None else 'N/A',
            str(signal) if signal is not None else 'N/A',
            round(float(entry_price), 5) if entry_price is not None and isinstance(entry_price, (int,float)) else 0.0,
            round(float(exit_price), 5) if exit_price is not None and isinstance(exit_price, (int,float)) else 0.0,
            round(float(pnl), 2) if pnl is not None and isinstance(pnl, (int,float)) else 0.0,
            str(entry_time_bot) if entry_time_bot is not None else 'N/A',
            round(float(sl_level), 5) if sl_level is not None and isinstance(sl_level, (int,float)) else 0.0,
            bool(trail_activated)
        ]
        
        logger.info(f"Pokus o zápis do CSV: {row_data}")
        
        file_exists_and_has_content = os.path.isfile(trades_csv_path) and os.path.getsize(trades_csv_path) > 0
        
        with open(trades_csv_path, 'a', newline='') as f:
            writer = csv.writer(f)
            if not file_exists_and_has_content:
                writer.writerow(header)
                logger.info(f"Vytvorený nový CSV súbor alebo pridaná hlavička do prázdneho: {trades_csv_path}")
            writer.writerow(row_data)
            logger.info(f"Úspešne zapísaný obchod do CSV: {trades_csv_path}")
            return True
    except PermissionError as pe: 
        logger.error(f"Chyba oprávnení pri zápise do CSV ({trades_csv_path}): {pe}", exc_info=True)
    except Exception as e:
        logger.error(f"Všeobecná chyba pri zapisovaní do CSV ({trades_csv_path}): {e}", exc_info=True)
    return False


def send_telegram(message: str):
    token = getattr(config, 'TELEGRAM_TOKEN', None)
    chat_id = getattr(config, 'TELEGRAM_CHAT_ID', None)

    if not token or not chat_id or token == 'VÁŠ_AKTUÁLNY_TOKEN' or token == 'TVOJ_TELEGRAM_TOKEN' \
       or chat_id == 'VÁŠ_AKTUÁLNY_CHAT_ID' or chat_id == 'TVOJ_CHAT_ID':
        logger.warning(f"Telegram token alebo Chat ID nie je (správne) nastavené. Skontrolujte config.py! Správa '{message[:70]}...' sa neodosiela.")
        return False
        
    url = f"https://api.telegram.org/bot{token}/sendMessage"
    payload = {'chat_id': chat_id, 'text': message}
    try:
        response = requests.post(url, data=payload, timeout=10)
        response.raise_for_status()
        logger.info(f"Telegram správa odoslaná (začiatok): {message[:70]}...")
        return True
    except requests.exceptions.RequestException as e:
        logger.error(f"Telegram notifikácia zlyhala (chyba requestu): {e}")
    except Exception as e_gen:
        logger.error(f"Neočekávaná chyba pri odosielaní Telegramu: {e_gen}", exc_info=True)
    return False

def get_front_contract_month():
    now = datetime.now()
    year = now.year
    month = now.month
    day = now.day
    
    # Pre kvartálne (H,M,U,Z) - Mar, Jun, Sep, Dec
    # Rollover sa často deje predchádzajúci mesiac, napr. druhý piatok (okolo 8.-14. dňa)
    if (month == 2 and day > 8) or month == 3 : target_q_month = 3
    elif (month == 5 and day > 8) or month == 6: target_q_month = 6
    elif (month == 8 and day > 8) or month == 9: target_q_month = 9
    elif (month == 11 and day > 8) or month == 12: target_q_month = 12
    else: 
        if month < 3: target_q_month = 3
        elif month < 6: target_q_month = 6
        elif month < 9: target_q_month = 9
        else: 
            target_q_month = 12 
            if month == 12 and day > 8: 
                 year +=1; target_q_month = 3

    if month > target_q_month and target_q_month == 3 and month >=10: 
         year += 1
    
    logger.debug(f"get_front_contract_month: now={now.strftime('%Y-%m-%d')}, target_q_month={target_q_month}, year={year}")
    return f"{year}{target_q_month:02d}"


def calc_pivots(df_daily: pd.DataFrame, instrument_symbol: str):
    if df_daily is None or len(df_daily) < 2:
        logger.warning(f"[{instrument_symbol}] Nedostatok denných dát na výpočet pivotov: {len(df_daily) if df_daily is not None else 'None'} riadkov")
        return None, None 
        
    prev_day_data = df_daily.iloc[-2]
    try:
        required_cols = ['high', 'low', 'close']
        for col in required_cols:
            if col not in prev_day_data or pd.isna(prev_day_data[col]):
                logger.error(f"[{instrument_symbol}] Chýbajúca alebo NaNs hodnota v stĺpci '{col}' pre výpočet pivotov. Dáta: {prev_day_data.to_dict() if isinstance(prev_day_data, pd.Series) else prev_day_data}")
                return None, None
        
        ph = float(prev_day_data['high'])
        pl = float(prev_day_data['low'])
        pc = float(prev_day_data['close'])

    except (TypeError, ValueError) as e:
        logger.error(f"[{instrument_symbol}] Neplatné cenové dáta (nie sú čísla) pre výpočet pivotov: {e}. Dáta: {prev_day_data.to_dict() if isinstance(prev_day_data, pd.Series) else prev_day_data}")
        return None, None

    rng = ph - pl
    if rng < -1e-9: 
        logger.error(f"[{instrument_symbol}] Chyba pri výpočte pivotov: High ({ph}) < Low ({pl}). Preskakujem pivoty.")
        return None, None
    if abs(rng) < 1e-9: 
        logger.warning(f"[{instrument_symbol}] Rozsah (Range) predchádzajúceho dňa je takmer 0 ({rng}). Pivoty budú rovné Close ({pc}).")
        return pc, pc

    h4 = pc + rng * 0.55
    l4 = pc - rng * 0.55

    price_format = ".4f" if instrument_symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
    logger.info(f"[{instrument_symbol}] Camarilla pivoty: Predch.deň H={ph:{price_format}}, L={pl:{price_format}}, C={pc:{price_format}}, Rng={rng:{price_format}} => H4={h4:{price_format}}, L4={l4:{price_format}}")
    return h4, l4


def wait_for_next_bar(timeframe_setting: str) -> datetime:
    try:
        parts = timeframe_setting.split()
        if len(parts) != 2: raise ValueError("Formát TIMEFRAME musí byť 'hodnota jednotka'")
        value = int(parts[0])
        unit = parts[1].lower()
        if 'hour' in unit: tf_minutes = value * 60
        elif 'min' in unit: tf_minutes = value
        else: raise ValueError(f"Neznáma jednotka časového rámca v '{timeframe_setting}'")
    except Exception as e:
        logger.error(f"Chybný TIMEFRAME '{timeframe_setting}': {e}. Používam 60 minút.")
        tf_minutes = 60
    
    if tf_minutes <= 0:
        logger.error(f"Neplatný tf_minutes ({tf_minutes}). Používam 60 minút.")
        tf_minutes = 60

    try:
        local_tz_server = datetime.now().astimezone().tzinfo
        if local_tz_server is None: 
            logger.warning("Nepodarilo sa automaticky zistiť lokálnu časovú zónu servera. Používam pytz.timezone('Europe/Bratislava'). Ak zlyhá, použijem UTC.")
            try:
                local_tz_server = pytz.timezone('Europe/Bratislava') 
            except pytz.exceptions.UnknownTimeZoneError:
                logger.error("Časová zóna 'Europe/Bratislava' nebola nájdená, používam UTC ako fallback pre výpočet ďalšej sviečky.")
                local_tz_server = pytz.utc
        now_local_aware = datetime.now(local_tz_server)
    except Exception as e_tz_detect_wait:
        logger.error(f"Chyba pri získavaní lokálnej časovej zóny: {e_tz_detect_wait}. Používam naivný čas (môže viesť k problémom s DST).")
        now_local_aware = datetime.now() 

    # Výpočet ďalšej sviečky založený na minútach od polnoci
    minutes_since_midnight_local = now_local_aware.hour * 60 + now_local_aware.minute
    current_bar_index_in_day = minutes_since_midnight_local // tf_minutes
    next_block_start_total_minutes = (current_bar_index_in_day + 1) * tf_minutes
    
    next_bar_hour_local = next_block_start_total_minutes // 60
    next_bar_minute_local = next_block_start_total_minutes % 60
    
    next_time_local_aware = now_local_aware.replace(
        hour=next_bar_hour_local % 24, 
        minute=next_bar_minute_local, 
        second=0, 
        microsecond=0
    )
    
    days_to_add = next_block_start_total_minutes // (24 * 60) # Koľko celých dní sme preskočili
    if days_to_add > 0:
        next_time_local_aware += timedelta(days=days_to_add)
    
    # Ak je vypočítaný čas v minulosti alebo rovnaký pre aktuálny deň, posunieme
    while next_time_local_aware <= now_local_aware:
        logger.debug(f"wait_for_next_bar: next_time_local_aware ({next_time_local_aware}) je <= now_local_aware ({now_local_aware}), pripočítavam {tf_minutes} minút.")
        next_time_local_aware += timedelta(minutes=tf_minutes)


    if next_time_local_aware.tzinfo is None and local_tz_server is not None:
        logger.warning("next_time_local_aware stratilo časovú zónu, priraďujem zistenú lokálnu zónu.")
        try:
            next_time_local_aware = local_tz_server.localize(next_time_local_aware, is_dst=None)
        except Exception as e_localize_fallback:
             logger.error(f"Chyba pri núdzovej lokalizácii next_time_local_aware: {e_localize_fallback}. Používam UTC.")
             next_time_local_aware = pytz.utc.localize(datetime(next_time_local_aware.year, next_time_local_aware.month, next_time_local_aware.day, 
                                                               next_time_local_aware.hour, next_time_local_aware.minute), is_dst=None)
    elif next_time_local_aware.tzinfo is None: 
         logger.warning("next_time_local_aware je naivné a local_tz_server je None, lokalizujem na UTC ako núdzové riešenie.")
         next_time_local_aware = pytz.utc.localize(next_time_local_aware, is_dst=None)


    wait_seconds = (next_time_local_aware - now_local_aware).total_seconds()
    wait_seconds = max(wait_seconds, 0.1) 

    max_sensible_wait = tf_minutes * 60 * 1.5 
    if wait_seconds > max_sensible_wait and tf_minutes > 0 :
        logger.warning(f"Vypočítaný čas čakania ({wait_seconds:.2f}s) je dlhší ako 1.5x TF ({tf_minutes}min). Obmedzujem na 3 sekundy.")
        wait_seconds = 3.0
    
    next_time_for_log_str = next_time_local_aware.strftime('%Y-%m-%d %H:%M:%S %Z') if next_time_local_aware.tzinfo else next_time_local_aware.strftime('%Y-%m-%d %H:%M:%S')
    logger.info(f"Čakám {wait_seconds:.2f}s do ďalšej sviečky o {next_time_for_log_str} (čas servera).")
    time.sleep(wait_seconds)
    
    if next_time_local_aware.tzinfo is None: # Finálna poistka pred konverziou na UTC
        logger.error("KRITICKÁ CHYBA: next_time_local_aware je stále naivný pred finálnou konverziou na UTC!")
        # Núdzový pokus vytvoriť UTC čas pre ďalší interval
        # Toto by sa nemalo stať, ak je logika vyššie správna
        current_utc_for_fallback = datetime.now(pytz.utc)
        blocks_from_midnight_utc = (current_utc_for_fallback.hour * 60 + current_utc_for_fallback.minute) // tf_minutes
        next_total_minutes_utc_fb = (blocks_from_midnight_utc + 1) * tf_minutes # Premenované
        next_hour_utc_fb = next_total_minutes_utc_fb // 60
        next_minute_utc_fb = next_total_minutes_utc_fb % 60
        next_time_utc_val = current_utc_for_fallback.replace(hour=next_hour_utc_fb % 24, minute=next_minute_utc_fb, second=0, microsecond=0)
        days_offset_utc_val_fb = next_total_minutes_utc_fb // (24 * 60) # Premenované
        if days_offset_utc_val_fb > 0: next_time_utc_val += timedelta(days=days_offset_utc_val_fb)
        while next_time_utc_val <= current_utc_for_fallback: next_time_utc_val += timedelta(minutes=tf_minutes)
        next_time_utc = next_time_utc_val
    else:
        next_time_utc = next_time_local_aware.astimezone(pytz.utc)

    logger.info(f"Teoretický začiatok novej sviečky (UTC): {next_time_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    return next_time_utc

def is_near_market_close():
    if not getattr(config, 'CHECK_EOD_CLOSURE_ENABLED', False):
        logger.debug("EOD uzatváranie je vypnuté v konfigurácii.")
        return False
    try:
        eastern = pytz.timezone('US/Eastern')
    except pytz.exceptions.UnknownTimeZoneError:
        logger.error("Časové pásmo 'US/Eastern' nebolo nájdené. Skontrolujte inštaláciu pytz.")
        return False

    now_utc = datetime.now(pytz.utc)
    now_et = now_utc.astimezone(eastern)
    
    market_close_hour_et = getattr(config, 'MARKET_CLOSE_HOUR', 17)
    close_minutes_param = getattr(config, 'CLOSE_MINUTES_BEFORE_END', 5)
    
    if now_et.weekday() >= 5: 
        logger.debug(f"Je víkend ({now_et.strftime('%A')}), EOD sa neuplatňuje.")
        return False
    
    close_time_et = now_et.replace(hour=market_close_hour_et, minute=0, second=0, microsecond=0)

    if now_et >= close_time_et: 
        logger.debug(f"Aktuálny čas ET {now_et.strftime('%H:%M:%S')} je už po zatváracom čase ET {close_time_et.strftime('%H:%M:%S')} pre tento deň.")
        return False 

    time_diff_minutes = (close_time_et - now_et).total_seconds() / 60
    is_eod_window = 0 < time_diff_minutes <= close_minutes_param

    if time_diff_minutes < (close_minutes_param + 60) : 
         logger.info(f"EOD Check: Aktuálny čas (ET): {now_et.strftime('%Y-%m-%d %H:%M:%S %Z')}, Zatvorenie trhu (ET): {close_time_et.strftime('%H:%M:%S')}, Zostáva: {time_diff_minutes:.1f}min. V EOD okne ({close_minutes_param}min): {is_eod_window}")
    else:
         logger.debug(f"EOD Check: Zostáva {time_diff_minutes:.1f}min do close. Mimo detailného EOD logovacieho okna.")
    
    return is_eod_window