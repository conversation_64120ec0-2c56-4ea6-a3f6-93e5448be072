#!/usr/bin/env python3
# camarilla_futures.py
# Harmonizovaný Multi-instrument Camarilla H4/L4 breakout bot pre futures kontrakty

import sys
import time
import logging
import os
import csv
from datetime import datetime, timedelta
import requests
import pandas as pd
import pytz
from ib_insync import IB, util, Future, MarketOrder, Order, Ticker

# ---------- KONFIGURÁCIA ----------
TIMEFRAME = '1 hour'          # '30 mins', '1 hour' alebo '4 hours'
QUANTITY = 1                   # Počet kontraktov na obchod

# Hodnoty pre futures kontrakty (v tickoch)
# Tieto hodnoty fungujú rovnako ako v TradingView stratégii
# SL v USD = počet tickov × hodnota ticku v USD
# Pre MES: 1 tick = 0.25 × 5 = 1.25 USD, pre M2K: 1 tick = 0.10 × 5 = 0.50 USD
SL_PTS_LONG     = 70   # Stop-loss pre LONG pozície
TRAIL_PTS_LONG  = 40   # O koľko sa musí cena pohnúť v tvoj prospech pre aktiváciu trailing stopu
TRAIL_OFFSET_LONG = 1  # Vzdialenosť, ktorú trailing stop udržiava od ceny po aktivácii
SL_PTS_SHORT    = 40   # Stop-loss pre SHORT pozície
TRAIL_PTS_SHORT = 20   # O koľko sa musí cena pohnúť v tvoj prospech pre aktiváciu trailing stopu
TRAIL_OFFSET_SHORT = 1 # Vzdialenosť, ktorú trailing stop udržiava od ceny po aktivácii

# Konfigurácia pre uzatváranie pozícií pred koncom obchodného dňa
CLOSE_MINUTES_BEFORE_END = 5  # Koľko minút pred koncom obchodného dňa uzavrieť pozície
MARKET_CLOSE_HOUR = 17        # Koniec obchodného dňa (17:00 ET)

# Futures kontrakty
INSTRUMENTS = [
    # CME
    {'symbol': 'M6A', 'exchange': 'CME', 'multiplier': 10000, 'secType': 'FUTURE'},  # Micro Australian Dollar
    {'symbol': 'M6B', 'exchange': 'CME', 'multiplier': 6250, 'secType': 'FUTURE'},   # Micro British Pound
    {'symbol': 'M2K', 'exchange': 'CME', 'multiplier': 5, 'secType': 'FUTURE'},      # Micro Russell 2000
    {'symbol': 'M6E', 'exchange': 'CME', 'multiplier': 12500, 'secType': 'FUTURE'},  # Micro Euro FX
    {'symbol': 'MES', 'exchange': 'CME', 'multiplier': 5, 'secType': 'FUTURE'},      # Micro E-mini S&P 500
    {'symbol': 'MNQ', 'exchange': 'CME', 'multiplier': 2, 'secType': 'FUTURE'},      # Micro E-mini Nasdaq-100
    # COMEX
    {'symbol': 'MGC', 'exchange': 'COMEX', 'multiplier': 10, 'secType': 'FUTURE'},   # Micro Gold
]

TELEGRAM_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '91797520'

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

ib = IB()
last_closed_conids = set()
TRADES_CSV = 'trades_log.csv'

# ---------- CSV logika ----------
def append_trade_to_csv(symbol, signal, entry_price, exit_price, pnl, entry_time, sl_level, trail_activated):
    try:
        header = ['symbol', 'signal', 'entry_price', 'exit_price', 'pnl', 'entry_time', 'sl_level', 'trail_activated']
        exists = os.path.isfile(TRADES_CSV)

        # Podrobné logovanie pre diagnostiku
        logging.info(f"Appending trade to CSV: {symbol}, {signal}, {entry_price}, {exit_price}, {pnl}, {entry_time}, {sl_level}, {trail_activated}")

        with open(TRADES_CSV, 'a', newline='') as f:
            writer = csv.writer(f)
            if not exists:
                writer.writerow(header)
                logging.info(f"Created new CSV file with header: {TRADES_CSV}")
            writer.writerow([symbol, signal, entry_price, exit_price, pnl, entry_time, sl_level, trail_activated])
            logging.info(f"Successfully wrote trade to CSV: {TRADES_CSV}")
    except Exception as e:
        logging.error(f"Error appending to CSV: {e}", exc_info=True)

# ---------- Telegram ----------
def send_telegram(message: str):
    url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage"
    payload = {'chat_id': TELEGRAM_CHAT_ID, 'text': message}
    try:
        requests.post(url, data=payload)
    except Exception as e:
        logging.error(f"Telegram notification failed: {e}")

# ---------- Pomocné funkcie ----------
def get_front_contract_month():
    """
    Získa front-month pre futures kontrakty.
    Pre Nasdaq futures (NQ, MNQ) sú to štandardné kvartálne kontrakty (H, M, U, Z).
    """
    now = datetime.now()
    year = now.year

    # Kvartálne mesiace (Mar, Jun, Sep, Dec)
    quarters = [3, 6, 9, 12]

    for m in quarters:
        if m > now.month:
            return f"{year}{m:02d}"

    # Ak sme v decembri, vrátime marec nasledujúceho roka
    return f"{year+1}03"

def fetch_contract(symbol, exchange, expiry=None, secType='FUTURE', currency='USD'):
    try:
        if secType == 'FUTURE':
            # Pre futures potrebujeme expiry
            shim = Future(symbol=symbol, exchange=exchange, currency=currency)
            cds = ib.reqContractDetails(shim)
            if not cds:
                logging.error(f"No {symbol} futures found on {exchange}")
                return None
            for cd in cds:
                c = cd.contract
                if c.lastTradeDateOrContractMonth and c.lastTradeDateOrContractMonth.startswith(expiry):
                    return c
            logging.error(f"Front-month {symbol} ({expiry}) not found on {exchange}")
            return None
        else:
            logging.error(f"Unsupported security type: {secType}")
            return None
    except Exception as e:
        logging.error(f"Error fetching contract for {symbol}: {e}", exc_info=True)
        return None

def calc_pivots(df: pd.DataFrame):
    """
    Vypočíta Camarilla h4 a l4 úrovne z denných dát.
    Používa posledný kompletný deň (predchádzajúci obchodný deň).

    Vzorec pre Camarilla úrovne:
    h4 = c + (h - l) * 0.55
    l4 = c - (h - l) * 0.55

    kde c, h, l sú close, high, low z predchádzajúceho dňa.
    """
    # Použijeme posledný kompletný deň (predposledný v datasete)
    if len(df) >= 2:
        idx = -2  # Predposledný deň (predchádzajúci obchodný deň)
    else:
        idx = -1  # Ak máme len jeden deň, použijeme ho

    ph, pl, pc = df['high'].iloc[idx], df['low'].iloc[idx], df['close'].iloc[idx]
    rng = ph - pl

    # Camarilla vzorec používa konštantu 0.55 (nie 1.1/2)
    h4 = pc + rng * 0.55
    l4 = pc - rng * 0.55

    # Logujeme hodnoty pre debugging s vhodným počtom desatinných miest podľa symbolu
    symbol = df.get('symbol', None)
    if symbol in ['M6A', 'M6B', 'M6E']:
        # Pre menové páry používame 4 desatinné miesta
        logging.info(f"Camarilla calculation for {symbol}: prev day high={ph:.4f}, low={pl:.4f}, close={pc:.4f}, range={rng:.4f}, h4={h4:.4f}, l4={l4:.4f}")
    else:
        # Pre ostatné kontrakty používame 2 desatinné miesta
        logging.info(f"Camarilla calculation: prev day high={ph:.2f}, low={pl:.2f}, close={pc:.2f}, range={rng:.2f}, h4={h4:.2f}, l4={l4:.2f}")

    return h4, l4

def wait_for_next_bar(tf_minutes: int):
    now = datetime.now()

    if tf_minutes >= 60:  # Pre hodinové časové rámce
        hours_in_tf = tf_minutes // 60
        hour = now.hour
        next_hour = ((hour // hours_in_tf) + 1) * hours_in_tf
        next_time = now.replace(minute=0, second=0, microsecond=0)
        if next_hour >= 24:
            next_time = next_time.replace(hour=0) + timedelta(days=1)
        else:
            next_time = next_time.replace(hour=next_hour)
    else:  # Pre minútové časové rámce
        minute = now.minute
        next_min = ((minute // tf_minutes) + 1) * tf_minutes
        next_time = now.replace(second=0, microsecond=0)
        if next_min >= 60:
            next_time = next_time.replace(minute=0) + timedelta(hours=1)
        else:
            next_time = next_time.replace(minute=next_min)

    wait = max((next_time - now).total_seconds(), 0)
    logging.info(f"Sleeping {int(wait)}s until next bar close at {next_time.strftime('%H:%M')}")
    time.sleep(wait)

def is_near_market_close(minutes_before=CLOSE_MINUTES_BEFORE_END):
    """
    Skontroluje, či sa blíži koniec obchodného dňa (17:00 ET).
    Automaticky zohľadňuje letný/zimný čas.

    Returns:
        bool: True ak je čas blízko konca obchodného dňa, inak False
    """
    # Definujeme časové pásmo ET (Eastern Time)
    eastern = pytz.timezone('US/Eastern')

    # Získame aktuálny čas v ET
    now_utc = datetime.now(pytz.utc)
    now_et = now_utc.astimezone(eastern)

    # Vytvoríme datetime objekt pre čas uzavretia dnes v ET
    close_today = now_et.replace(
        hour=MARKET_CLOSE_HOUR,
        minute=0,
        second=0,
        microsecond=0
    )

    # Ak už je po zatváracom čase, vrátime False
    if now_et > close_today:
        return False

    # Vypočítame rozdiel v minútach
    time_diff = (close_today - now_et).total_seconds() / 60

    # Logujeme informácie o čase každú hodinu (aby sme nepreťažovali log)
    if now_et.minute < 2:  # Logujeme len v prvých 2 minútach každej hodiny
        logging.info(f"Current time (ET): {now_et.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.info(f"Market close time (ET): {close_today.strftime('%H:%M:%S')}")
        logging.info(f"Minutes until market close: {time_diff:.1f}")

    # Vrátime True, ak je rozdiel menší ako požadovaný počet minút
    return time_diff <= minutes_before

def close_all_positions_before_market_close():
    """
    Uzavrie všetky otvorené pozície pred koncom obchodného dňa.
    """
    # Ak nie je blízko konca obchodného dňa, nič nerobíme
    if not is_near_market_close():
        return

    logging.info(f"Market close approaching (within {CLOSE_MINUTES_BEFORE_END} minutes), checking for open positions to close")

    for inst in INSTRUMENTS:
        if not inst['inPosition'] or not inst['contract']:
            continue

        try:
            logging.warning(f"Closing position for {inst['symbol']} due to approaching market close")

            # Vytvoríme market order na uzavretie pozície
            action = 'SELL' if inst['entry_signal'] == 'LONG' else 'BUY'
            close_order = MarketOrder(action, QUANTITY)
            close_order.outsideRth = True
            ib.placeOrder(inst['contract'], close_order)

            # Zaznamenáme uzavretie pozície
            # Použijeme aktuálnu cenu alebo close z poslednej sviečky
            current_price = None
            try:
                tickers = ib.reqTickers(inst['contract'])
                if tickers and tickers[0].last:
                    current_price = tickers[0].last
            except Exception as e:
                logging.error(f"Error getting current price for {inst['symbol']}: {e}")

            if not current_price:
                # Ak nemáme aktuálnu cenu, skúsime získať poslednú cenu z historických dát
                try:
                    bars = ib.reqHistoricalData(
                        inst['contract'], '', '60 S', '1 min', 'MIDPOINT', True
                    )
                    if bars:
                        current_price = bars[-1].close
                except Exception as e:
                    logging.error(f"Error getting historical data for {inst['symbol']}: {e}")

            # Ak stále nemáme cenu, použijeme entry_price (nie ideálne, ale lepšie ako nič)
            exit_price = current_price if current_price else inst['entry_price']

            pts = (exit_price - inst['entry_price']) if inst['entry_signal'] == 'LONG' else (inst['entry_price'] - exit_price)
            profit = pts * inst['multiplier'] * QUANTITY

            # Formátovanie s vhodným počtom desatinných miest podľa symbolu
            if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                # Pre menové páry používame 4 desatinné miesta
                msg = f"Closed {inst['entry_signal']} on {inst['symbol']} @ {exit_price:.4f}, P/L {profit:.2f} USD (due to market close)"
            else:
                # Pre ostatné kontrakty používame 2 desatinné miesta
                msg = f"Closed {inst['entry_signal']} on {inst['symbol']} @ {exit_price:.2f}, P/L {profit:.2f} USD (due to market close)"
            logging.info(msg)
            send_telegram(msg)

            # Aktualizujeme stav
            inst['inPosition'] = False
            inst['trailing_set'] = False

            # Zaznamenáme obchod do CSV
            try:
                append_trade_to_csv(
                    inst['symbol'], inst['entry_signal'], inst['entry_price'], exit_price, profit,
                    inst['entry_time'],
                    inst['entry_price'] - SL_PTS_LONG if inst['entry_signal'] == 'LONG' else inst['entry_price'] + SL_PTS_SHORT,
                    inst['trailing_set']
                )
            except Exception as e:
                logging.error(f"Failed to append trade to CSV: {e}")

        except Exception as e:
            logging.error(f"Error closing position before market close for {inst['symbol']}: {e}", exc_info=True)

def place_bracket(inst, signal, entry_price):
    """
    Otvorí pozíciu s market order a nastaví stop-loss.
    Trailing stop sa nastaví neskôr, keď cena dosiahne offset.
    """
    sym = inst['symbol']
    action = 'BUY' if signal == 'LONG' else 'SELL'

    # Definícia tick size pre rôzne kontrakty
    tick_sizes = {
        'M6A': 0.00005,  # Micro AUD/USD - 0.5 pip
        'M6B': 0.0001,   # Micro GBP/USD - 1 pip
        'M6C': 0.00005,  # Micro CAD/USD - 0.5 pip
        'M6E': 0.00005,  # Micro EUR/USD - 0.5 pip
        'MES': 0.25,     # Micro E-mini S&P 500 - 0.25 point
        'MNQ': 0.25,     # Micro E-mini Nasdaq-100 - 0.25 point
        'M2K': 0.1,      # Micro E-mini Russell 2000 - 0.1 point
        'MGC': 0.1       # Micro Gold - 0.1 point
    }

    # Získanie tick size pre daný kontrakt
    tick_size = tick_sizes.get(sym, 1.0)  # Default 1.0 ak nie je definované

    # Konverzia tickov na cenu
    sl_pts_long_price = SL_PTS_LONG * tick_size
    sl_pts_short_price = SL_PTS_SHORT * tick_size
    trail_pts_long_price = TRAIL_PTS_LONG * tick_size
    trail_pts_short_price = TRAIL_PTS_SHORT * tick_size
    trail_offset_long_price = TRAIL_OFFSET_LONG * tick_size
    trail_offset_short_price = TRAIL_OFFSET_SHORT * tick_size

    logging.info(f"Using tick size {tick_size} for {sym}. SL: {sl_pts_long_price}/{sl_pts_short_price}, Trail: {trail_pts_long_price}/{trail_pts_short_price}, Offset: {trail_offset_long_price}/{trail_offset_short_price}")

    # Vytvorenie hlavného market orderu
    parent = MarketOrder(action, QUANTITY)
    parent.transmit = False
    parent.outsideRth = True  # Povolíme obchodovanie mimo RTH

    # Umiestnenie hlavného orderu a čakanie na jeho spracovanie
    trade = ib.placeOrder(inst['contract'], parent)
    logging.info(f"Placed parent order for {inst['symbol']}: {trade}")

    # Počkáme na pridelenie orderId
    for _ in range(5):  # Maximálne 5 sekúnd
        if hasattr(parent, 'orderId') and parent.orderId:
            break
        ib.sleep(1)

    if not hasattr(parent, 'orderId') or not parent.orderId:
        logging.error(f"Failed to get orderId for parent order for {inst['symbol']}")
        # Skúsime získať orderId z trade objektu
        if hasattr(trade, 'order') and hasattr(trade.order, 'orderId'):
            parent.orderId = trade.order.orderId
            logging.info(f"Retrieved orderId from trade object: {parent.orderId}")
        else:
            # Ak nemáme orderId, nemôžeme pokračovať
            raise Exception(f"Cannot create bracket order for {inst['symbol']} - no orderId assigned")

    # Nastavenie stop-loss ceny
    sl_price = entry_price - sl_pts_long_price if signal == 'LONG' else entry_price + sl_pts_short_price

    # Vytvorenie stop-loss orderu
    stop = Order(
        orderType='STP',
        action=('SELL' if signal == 'LONG' else 'BUY'),
        totalQuantity=QUANTITY,
        auxPrice=sl_price,
        parentId=parent.orderId,
        transmit=True
    )
    stop.outsideRth = True

    # Umiestnenie stop-loss orderu
    sl_trade = ib.placeOrder(inst['contract'], stop)
    logging.info(f"Placed stop-loss order for {inst['symbol']}: {sl_trade}")

    # Uloženie informácií o stop-loss orderi pre neskoršie použitie
    inst['sl_order_id'] = stop.orderId if hasattr(stop, 'orderId') else None
    inst['sl_price'] = sl_price

    # Nastavenie aktivačnej ceny pre trailing stop
    inst['trail_offset_reached'] = False
    if signal == 'LONG':
        inst['trail_activation_price'] = entry_price + trail_pts_long_price  # Cena, pri ktorej sa aktivuje trailing stop
        inst['trail_offset'] = trail_offset_long_price  # Uložíme offset pre neskoršie použitie
    else:  # SHORT
        inst['trail_activation_price'] = entry_price - trail_pts_short_price  # Cena, pri ktorej sa aktivuje trailing stop
        inst['trail_offset'] = trail_offset_short_price  # Uložíme offset pre neskoršie použitie

    # Formátovanie s vhodným počtom desatinných miest podľa symbolu
    if sym in ['M6A', 'M6B', 'M6E']:
        # Pre menové páry používame 4 desatinné miesta
        msg = f"Opened {signal} on {sym} @ {entry_price:.4f}, SL @ {sl_price:.4f}, Trail will activate at {inst['trail_activation_price']:.4f}"
    else:
        # Pre ostatné kontrakty používame 2 desatinné miesta
        msg = f"Opened {signal} on {sym} @ {entry_price:.2f}, SL @ {sl_price:.2f}, Trail will activate at {inst['trail_activation_price']:.2f}"

    logging.info(msg)
    send_telegram(msg)

# ---------- Hlavný cyklus ----------
def main_loop():
    global last_closed_conids

    # Pokus o pripojenie k IB Gateway s opakovaním
    max_connect_attempts = 3
    connect_attempt = 0

    while connect_attempt < max_connect_attempts:
        try:
            if ib.isConnected():
                logging.info("Already connected to IB Gateway")
            else:
                ib.connect('127.0.0.1', 4001, clientId=1)
                logging.info("Connected to IB Gateway on EH port 4001")

            # Ak sme sa úspešne pripojili, prerušíme cyklus
            break
        except Exception as e:
            connect_attempt += 1
            logging.error(f"Connection attempt {connect_attempt}/{max_connect_attempts} failed: {e}")
            if connect_attempt < max_connect_attempts:
                time.sleep(5)  # Počkáme 5 sekúnd pred ďalším pokusom
            else:
                raise  # Ak všetky pokusy zlyhali, vyvoláme výnimku

    # Preskočíme volanie reqAccountUpdates, ktoré pravdepodobne spôsobuje problém
    logging.info("Skipping account updates disable - this might be causing issues")

    expiry = get_front_contract_month()
    for inst in INSTRUMENTS:
        logging.info(f"Fetching contract for {inst['symbol']} on {inst['exchange']}")
        try:
            # Určenie typu inštrumentu (FUTURE)
            secType = inst.get('secType', 'FUTURE')
            currency = inst.get('currency', 'USD')

            if secType == 'FUTURE':
                inst['contract'] = fetch_contract(inst['symbol'], inst['exchange'], expiry, secType, currency)
                if not inst['contract']:
                    logging.error(f"Failed to fetch contract for {inst['symbol']}, skipping")
                    continue
            else:
                logging.error(f"Unsupported security type: {secType} for {inst['symbol']}, skipping")
                continue

            inst['inPosition'] = False
            inst['trailing_set'] = False
            inst['trail_offset_reached'] = False
            inst['sl_order_id'] = None
            inst['sl_price'] = 0

            contract_info = inst['contract'].localSymbol if hasattr(inst['contract'], 'localSymbol') else inst['contract'].symbol
            logging.info(f"Initialized {inst['symbol']} on {inst['exchange']} {contract_info}")
        except Exception as e:
            logging.error(f"Error fetching contract for {inst['symbol']}: {e}", exc_info=True)
            continue  # Pokračujeme s ďalšími inštrumentmi namiesto ukončenia programu

    logging.info(f"Calculating timeframe minutes from {TIMEFRAME}")
    if 'hour' in TIMEFRAME:
        hours = int(TIMEFRAME.split()[0])
        tf_minutes = hours * 60
    else:
        tf_minutes = int(TIMEFRAME.split()[0])
    logging.info(f"Timeframe minutes: {tf_minutes}")
    logging.info("About to wait for next bar")
    wait_for_next_bar(tf_minutes)

    while True:
        # Kontrola a uzavretie pozícií pred koncom obchodného dňa
        close_all_positions_before_market_close()

        # Kontrola rollover pre futures kontrakty
        new_expiry = get_front_contract_month()
        if new_expiry != expiry:
            logging.info(f"Detected rollover from {expiry} to {new_expiry}")
            expiry = new_expiry
            for inst in INSTRUMENTS:
                secType = inst.get('secType', 'FUTURE')
                if secType == 'FUTURE':  # Rollover sa týka len futures
                    try:
                        old_symbol = inst['contract'].localSymbol if hasattr(inst['contract'], 'localSymbol') else inst['contract'].symbol
                        logging.info(f"Rolling over {inst['symbol']} from {old_symbol}")
                        currency = inst.get('currency', 'USD')
                        new_contract = fetch_contract(inst['symbol'], inst['exchange'], expiry, secType, currency)
                        if not new_contract:
                            logging.error(f"Failed to fetch new contract for {inst['symbol']}, skipping rollover")
                            continue

                        # Ak máme otvorenú pozíciu, musíme ju najprv zavrieť
                        if inst['inPosition']:
                            logging.warning(f"Position open during rollover for {inst['symbol']}, closing it first")
                            try:
                                # Vytvoríme market order na uzavretie pozície
                                action = 'SELL' if inst['entry_signal'] == 'LONG' else 'BUY'
                                close_order = MarketOrder(action, QUANTITY)
                                close_order.outsideRth = True
                                ib.placeOrder(inst['contract'], close_order)
                                logging.info(f"Closed position for {inst['symbol']} due to rollover")
                                send_telegram(f"⚠️ Closed position for {inst['symbol']} due to rollover")
                            except Exception as close_err:
                                logging.error(f"Error closing position during rollover: {close_err}")

                        # Aktualizujeme kontrakt a resetujeme stav
                        inst['contract'] = new_contract
                        inst['inPosition'] = False
                        inst['trailing_set'] = False
                        inst['trail_offset_reached'] = False
                        inst['sl_order_id'] = None
                        inst['sl_price'] = 0
                        new_symbol = inst['contract'].localSymbol if hasattr(inst['contract'], 'localSymbol') else inst['contract'].symbol
                        logging.info(f"Rollover {inst['symbol']}: now {new_symbol}")
                    except Exception as e:
                        logging.error(f"Error during rollover for {inst['symbol']}: {e}", exc_info=True)
                        # Pokračujeme s ďalšími inštrumentmi namiesto ukončenia programu
                        continue

        for inst in INSTRUMENTS:
            try:
                c = inst['contract']
                if not c:
                    logging.warning(f"No valid contract for {inst['symbol']}, skipping")
                    continue

                # Získanie denných dát s opakovaním
                max_retries = 3
                retry_count = 0
                daily = None

                # Používame ASK s useRTH=False, pretože poskytuje najpresnejšie hodnoty pre futures kontrakty
                whatToShow_options = ['ASK']

                for whatToShow in whatToShow_options:
                    if daily is not None:
                        break  # Ak sme už získali dáta, prerušíme cyklus

                    logging.info(f"Trying to get daily data for {inst['symbol']} using {whatToShow}")
                    retry_count = 0

                    while retry_count < max_retries and daily is None:
                        try:
                            # Získame viac dní, aby sme mali istotu, že máme dostatok dát
                            # Použijeme RTH=False, aby sme získali presnejšie dáta vrátane obchodov mimo bežných obchodných hodín
                            daily = ib.reqHistoricalData(c, '', durationStr='5 D', barSizeSetting='1 day',
                                                        whatToShow=whatToShow, useRTH=False)
                        except Exception as e:
                            retry_count += 1
                            logging.warning(f"Error getting daily data for {inst['symbol']} using {whatToShow} (attempt {retry_count}/{max_retries}): {e}")
                            if retry_count < max_retries:
                                # Skontrolujeme pripojenie a prípadne sa znovu pripojíme
                                if not ib.isConnected():
                                    logging.warning("Connection lost, attempting to reconnect...")
                                    try:
                                        ib.disconnect()
                                        time.sleep(2)
                                        ib.connect('127.0.0.1', 4001, clientId=1)
                                        logging.info("Reconnected to IB Gateway")
                                    except Exception as conn_err:
                                        logging.error(f"Failed to reconnect: {conn_err}")
                                time.sleep(2)  # Krátka pauza pred ďalším pokusom

                if not daily:
                    logging.warning(f"Failed to get daily data for {inst['symbol']} after {max_retries} attempts")
                    continue

                # Konverzia na DataFrame
                df_d = util.df(daily)

                # Logujeme získané denné dáta pre debugging
                logging.info(f"Got {len(df_d)} daily bars for {inst['symbol']}")
                for i, row in df_d.iterrows():
                    # Formátovanie dátumu
                    if hasattr(i, 'strftime'):
                        date_str = i.strftime('%Y-%m-%d')
                    else:
                        date_str = str(i)[:10]  # Berieme prvých 10 znakov, čo by malo byť YYYY-MM-DD

                    # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                    if inst['symbol'] in ['M6A', 'M6B']:
                        # Pre menové páry používame 4 desatinné miesta
                        logging.info(f"Daily bar {i}: date={date_str} open={row['open']:.4f} high={row['high']:.4f} low={row['low']:.4f} close={row['close']:.4f}")
                    else:
                        # Pre ostatné kontrakty používame 2 desatinné miesta
                        logging.info(f"Daily bar {i}: date={date_str} open={row['open']:.2f} high={row['high']:.2f} low={row['low']:.2f} close={row['close']:.2f}")

                if len(df_d) < 2:
                    logging.warning(f"Insufficient daily data for {inst['symbol']}: {len(df_d)} bars")
                    continue

                h4_d, l4_d = calc_pivots(df_d)

                # Získanie dát pre časový rámec s opakovaním
                retry_count = 0
                bars = None

                # Skúsime rôzne nastavenia whatToShow
                for whatToShow in whatToShow_options:
                    if bars is not None:
                        break  # Ak sme už získali dáta, prerušíme cyklus

                    logging.info(f"Trying to get {TIMEFRAME} data for {inst['symbol']} using {whatToShow}")
                    retry_count = 0

                    while retry_count < max_retries and bars is None:
                        try:
                            # Použijeme RTH=False, aby sme získali presnejšie dáta vrátane obchodov mimo bežných obchodných hodín
                            bars = ib.reqHistoricalData(c, '', durationStr='1 D', barSizeSetting=TIMEFRAME,
                                                       whatToShow=whatToShow, useRTH=False)
                            if bars:
                                logging.info(f"Successfully got {TIMEFRAME} data for {inst['symbol']} using {whatToShow}")
                        except Exception as e:
                            retry_count += 1
                            logging.warning(f"Error getting {TIMEFRAME} data for {inst['symbol']} using {whatToShow} (attempt {retry_count}/{max_retries}): {e}")
                            if retry_count < max_retries:
                                # Skontrolujeme pripojenie a prípadne sa znovu pripojíme
                                if not ib.isConnected():
                                    logging.warning("Connection lost, attempting to reconnect...")
                                    try:
                                        ib.disconnect()
                                        time.sleep(2)
                                        ib.connect('127.0.0.1', 4001, clientId=1)
                                        logging.info("Reconnected to IB Gateway")
                                    except Exception as conn_err:
                                        logging.error(f"Failed to reconnect: {conn_err}")
                                time.sleep(2)  # Krátka pauza pred ďalším pokusom

                if not bars:
                    logging.warning(f"Failed to get {TIMEFRAME} data for {inst['symbol']} after {max_retries} attempts")
                    continue

                df = util.df(bars)
                if len(df) < 2:
                    logging.warning(f"Insufficient {TIMEFRAME} data for {inst['symbol']}: {len(df)} bars")
                    continue

                df['ema8'] = df['close'].ewm(span=8, adjust=False).mean()

                # Získanie poslednej UKONČENEJ sviečky
                # df.iloc[-1] je aktuálna, práve sa tvoriaca sviečka
                # df.iloc[-2] je posledná plne ukončená sviečka
                last_completed_bar = df.iloc[-2]
                lc = last_completed_bar['close']
                lo = last_completed_bar['open']
                le = last_completed_bar['ema8'] # EMA pre túto ukončenú sviečku

                # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                    # Pre menové páry používame 4 desatinné miesta
                    logging.info(f"{inst['symbol']} last bar: O={lo:.4f} C={lc:.4f} ema8={le:.4f} h4_d={h4_d:.4f} l4_d={l4_d:.4f}")
                else:
                    # Pre ostatné kontrakty používame 2 desatinné miesta
                    logging.info(f"{inst['symbol']} last bar: O={lo:.2f} C={lc:.2f} ema8={le:.2f} h4_d={h4_d:.2f} l4_d={l4_d:.2f}")

                # Pôvodná logika - NEMENENÁ
                longSig  = lc > h4_d and lo < h4_d and lc > le
                shortSig = lc < l4_d and lo > l4_d and lc < le

                # Len pridávame podrobné logovanie pre diagnostiku bez zmeny logiky
                logging.info(f"{inst['symbol']} signal conditions: longSig={longSig}, shortSig={shortSig}")

                # Podrobné logovanie jednotlivých podmienok pre lepšiu diagnostiku
                # Logujeme pre všetky inštrumenty, nielen MGC
                logging.info(f"{inst['symbol']} detailed conditions for LONG: lc > h4_d = {lc} > {h4_d} = {lc > h4_d}")
                logging.info(f"{inst['symbol']} detailed conditions for LONG: lo < h4_d = {lo} < {h4_d} = {lo < h4_d}")
                logging.info(f"{inst['symbol']} detailed conditions for LONG: lc > le = {lc} > {le} = {lc > le}")

                logging.info(f"{inst['symbol']} detailed conditions for SHORT: lc < l4_d = {lc} < {l4_d} = {lc < l4_d}")
                logging.info(f"{inst['symbol']} detailed conditions for SHORT: lo > l4_d = {lo} > {l4_d} = {lo > l4_d}")
                logging.info(f"{inst['symbol']} detailed conditions for SHORT: lc < le = {lc} < {le} = {lc < le}")

                # Kontrola existujúcich pozícií pred otvorením novej pozície
                try:
                    current_positions = ib.positions()
                    has_position = any(p.contract.conId == c.conId for p in current_positions)

                    # Aktualizujeme stav inPosition podľa skutočných pozícií
                    if has_position and not inst['inPosition']:
                        logging.warning(f"Position for {inst['symbol']} exists but was not tracked by bot. Updating state.")
                        inst['inPosition'] = True
                    elif not has_position and inst['inPosition']:
                        logging.warning(f"Position for {inst['symbol']} was closed but not detected by bot. Updating state.")
                        inst['inPosition'] = False
                        inst['trailing_set'] = False
                except Exception as e:
                    logging.error(f"Error checking positions before entry for {inst['symbol']}: {e}")
                    # Ak nemôžeme skontrolovať pozície, radšej neotvárame novú
                    continue

                if longSig and not inst['inPosition']:
                    # Ešte raz skontrolujeme, či nemáme pozíciu
                    has_position_recheck = any(p.contract.conId == c.conId for p in current_positions)
                    logging.info(f"LONG signal for {inst['symbol']}, has_position_recheck={has_position_recheck}")

                    if not has_position_recheck:
                        try:
                            logging.info(f"Placing LONG bracket order for {inst['symbol']} at {lc}")
                            place_bracket(inst, 'LONG', lc)
                            inst['inPosition']   = True
                            inst['entry_price']  = lc
                            inst['entry_signal'] = 'LONG'
                            inst['entry_time']   = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            inst['entry_timestamp'] = time.time()  # Pridáme timestamp pre sledovanie času otvorenia
                            inst['trailing_set'] = False
                            inst['trail_offset_reached'] = False
                            logging.info(f"Position tracking initialized for LONG on {inst['symbol']} at {inst['entry_timestamp']}")

                            # Skontrolujeme, či sa pozícia naozaj otvorila
                            time.sleep(2)  # Počkáme 2 sekundy na spracovanie orderu
                            positions_after = ib.positions()
                            position_opened = any(p.contract.conId == c.conId for p in positions_after)
                            logging.info(f"Position opened check for {inst['symbol']}: {position_opened}")
                        except Exception as e:
                            logging.error(f"Error placing LONG bracket order for {inst['symbol']}: {e}", exc_info=True)
                    else:
                        logging.warning(f"Skipping LONG signal for {inst['symbol']} because position already exists")
                elif shortSig and not inst['inPosition']:
                    # Ešte raz skontrolujeme, či nemáme pozíciu
                    has_position_recheck = any(p.contract.conId == c.conId for p in current_positions)
                    logging.info(f"SHORT signal for {inst['symbol']}, has_position_recheck={has_position_recheck}")

                    if not has_position_recheck:
                        try:
                            logging.info(f"Placing SHORT bracket order for {inst['symbol']} at {lc}")
                            place_bracket(inst, 'SHORT', lc)
                            inst['inPosition']   = True
                            inst['entry_price']  = lc
                            inst['entry_signal'] = 'SHORT'
                            inst['entry_time']   = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            inst['entry_timestamp'] = time.time()  # Pridáme timestamp pre sledovanie času otvorenia
                            inst['trailing_set'] = False
                            inst['trail_offset_reached'] = False
                            logging.info(f"Position tracking initialized for SHORT on {inst['symbol']} at {inst['entry_timestamp']}")

                            # Skontrolujeme, či sa pozícia naozaj otvorila
                            time.sleep(2)  # Počkáme 2 sekundy na spracovanie orderu
                            positions_after = ib.positions()
                            position_opened = any(p.contract.conId == c.conId for p in positions_after)
                            logging.info(f"Position opened check for {inst['symbol']}: {position_opened}")
                        except Exception as e:
                            logging.error(f"Error placing SHORT bracket order for {inst['symbol']}: {e}", exc_info=True)
                    else:
                        logging.warning(f"Skipping SHORT signal for {inst['symbol']} because position already exists")

                if inst['inPosition'] and not inst['trailing_set']:
                    try:
                        logging.info(f"Checking trailing stop condition for {inst['symbol']}")

                        # Skúsime získať aktuálnu cenu viacerými spôsobmi
                        current_price = None

                        # Spôsob 1: Použitie reqMktData
                        ticker = None
                        try:
                            ticker = ib.reqMktData(inst['contract'], '', False, False)
                            # Počkáme dlhšie na získanie dát (3 sekundy namiesto 1)
                            for _ in range(3):
                                ib.sleep(1)
                                if ticker.last:
                                    current_price = ticker.last
                                    break
                        except Exception as e:
                            logging.error(f"Error requesting market data for {inst['symbol']}: {e}")
                        finally:
                            if ticker:
                                try:
                                    ib.cancelMktData(inst['contract'])
                                except:
                                    pass

                        # Spôsob 2: Ak nemáme cenu z reqMktData, skúsime reqTickers
                        if not current_price:
                            try:
                                tickers = ib.reqTickers(inst['contract'])
                                if tickers and tickers[0].last:
                                    current_price = tickers[0].last
                                    logging.info(f"Got price from reqTickers for {inst['symbol']}: {current_price}")
                            except Exception as e:
                                logging.error(f"Error requesting tickers for {inst['symbol']}: {e}")

                        # Spôsob 3: Ak stále nemáme cenu, skúsime použiť close z poslednej sviečky
                        if not current_price:
                            current_price = lc
                            logging.warning(f"Using last bar close price for {inst['symbol']}: {current_price}")

                        if not current_price:
                            logging.warning(f"Could not get current price for {inst['symbol']}, cannot check trailing stop condition")
                            continue

                        # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                        if 'entry_price' in inst:
                            if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                                # Pre menové páry používame 4 desatinné miesta
                                logging.info(f"Current price for {inst['symbol']}: {current_price:.4f}, entry price: {inst['entry_price']:.4f}")
                            else:
                                # Pre ostatné kontrakty používame 2 desatinné miesta
                                logging.info(f"Current price for {inst['symbol']}: {current_price:.2f}, entry price: {inst['entry_price']:.2f}")
                        else:
                            if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                                # Pre menové páry používame 4 desatinné miesta
                                logging.info(f"Current price for {inst['symbol']}: {current_price:.4f}, entry price not set")
                            else:
                                # Pre ostatné kontrakty používame 2 desatinné miesta
                                logging.info(f"Current price for {inst['symbol']}: {current_price:.2f}, entry price not set")

                        # Kontrola podmienky pre aktiváciu trailing stopu
                        trail_condition_met = False

                        # Kontrola, či existujú potrebné kľúče
                        if 'entry_signal' not in inst or 'trail_activation_price' not in inst:
                            logging.warning(f"Missing entry_signal or trail_activation_price for {inst['symbol']}, skipping trail condition check")
                            continue

                        if inst['entry_signal'] == 'LONG':
                            trail_condition_met = current_price >= inst['trail_activation_price']
                            # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                            if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                                # Pre menové páry používame 4 desatinné miesta
                                logging.info(f"LONG trail condition: {current_price:.4f} >= {inst['trail_activation_price']:.4f}: {trail_condition_met}")
                            else:
                                # Pre ostatné kontrakty používame 2 desatinné miesta
                                logging.info(f"LONG trail condition: {current_price:.2f} >= {inst['trail_activation_price']:.2f}: {trail_condition_met}")
                        elif inst['entry_signal'] == 'SHORT':
                            trail_condition_met = current_price <= inst['trail_activation_price']
                            # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                            if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                                # Pre menové páry používame 4 desatinné miesta
                                logging.info(f"SHORT trail condition: {current_price:.4f} <= {inst['trail_activation_price']:.4f}: {trail_condition_met}")
                            else:
                                # Pre ostatné kontrakty používame 2 desatinné miesta
                                logging.info(f"SHORT trail condition: {current_price:.2f} <= {inst['trail_activation_price']:.2f}: {trail_condition_met}")

                        if trail_condition_met:
                            try:
                                if inst['entry_signal'] == 'LONG':
                                    # Zrušíme pôvodný stop-loss order, ak existuje
                                    if inst['sl_order_id']:
                                        try:
                                            # Priamo použijeme ID orderu, nie objekt
                                            ib.cancelOrder(inst['sl_order_id'])
                                            logging.info(f"Cancelled original stop-loss order {inst['sl_order_id']} for {inst['symbol']}")
                                        except Exception as cancel_err:
                                            logging.error(f"Error cancelling stop-loss order {inst['sl_order_id']}: {cancel_err}")
                                            # Pokúsime sa vytvoriť Order objekt s orderId, ak by to bolo potrebné
                                            try:
                                                # Použijeme už importovaný Order objekt
                                                cancel_order = Order()
                                                cancel_order.orderId = inst['sl_order_id']
                                                ib.cancelOrder(cancel_order)
                                                logging.info(f"Cancelled original stop-loss order {inst['sl_order_id']} for {inst['symbol']} using Order object")
                                            except Exception as alt_cancel_err:
                                                logging.error(f"Alternative method also failed: {alt_cancel_err}")

                                    # Vytvoríme trailing stop order s uloženým offsetom
                                    trail = Order(orderType='TRAIL', action='SELL', totalQuantity=QUANTITY, auxPrice=inst['trail_offset'], transmit=True)
                                    trail.outsideRth = True
                                    ib.placeOrder(inst['contract'], trail)
                                    inst['trailing_set'] = True
                                    # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                                    if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                                        # Pre menové páry používame 4 desatinné miesta
                                        msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.4f} with trail offset {inst['trail_offset']:.4f}"
                                    else:
                                        # Pre ostatné kontrakty používame 2 desatinné miesta
                                        msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.2f} with trail offset {inst['trail_offset']:.2f}"

                                    logging.info(msg)
                                    send_telegram(f"🔒 {msg}")
                                elif inst['entry_signal'] == 'SHORT':
                                    # Zrušíme pôvodný stop-loss order, ak existuje
                                    if inst['sl_order_id']:
                                        try:
                                            # Priamo použijeme ID orderu, nie objekt
                                            ib.cancelOrder(inst['sl_order_id'])
                                            logging.info(f"Cancelled original stop-loss order {inst['sl_order_id']} for {inst['symbol']}")
                                        except Exception as cancel_err:
                                            logging.error(f"Error cancelling stop-loss order {inst['sl_order_id']}: {cancel_err}")
                                            # Pokúsime sa vytvoriť Order objekt s orderId, ak by to bolo potrebné
                                            try:
                                                # Použijeme už importovaný Order objekt
                                                cancel_order = Order()
                                                cancel_order.orderId = inst['sl_order_id']
                                                ib.cancelOrder(cancel_order)
                                                logging.info(f"Cancelled original stop-loss order {inst['sl_order_id']} for {inst['symbol']} using Order object")
                                            except Exception as alt_cancel_err:
                                                logging.error(f"Alternative method also failed: {alt_cancel_err}")

                                    # Vytvoríme trailing stop order s uloženým offsetom
                                    trail = Order(orderType='TRAIL', action='BUY', totalQuantity=QUANTITY, auxPrice=inst['trail_offset'], transmit=True)
                                    trail.outsideRth = True
                                    ib.placeOrder(inst['contract'], trail)
                                    inst['trailing_set'] = True
                                    # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                                    if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                                        # Pre menové páry používame 4 desatinné miesta
                                        msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.4f} with trail offset {inst['trail_offset']:.4f}"
                                    else:
                                        # Pre ostatné kontrakty používame 2 desatinné miesta
                                        msg = f"Trailing stop activated on {inst['symbol']} @ {current_price:.2f} with trail offset {inst['trail_offset']:.2f}"

                                    logging.info(msg)
                                    send_telegram(f"🔒 {msg}")
                            except Exception as e:
                                logging.error(f"Error setting trailing stop for {inst['symbol']}: {e}")
                    except Exception as e:
                        logging.error(f"Error checking trailing stop condition for {inst['symbol']}: {e}", exc_info=True)
            except Exception as e:
                logging.error(f"Error processing {inst['symbol']}: {e}", exc_info=True)
                continue

            # Kontrola uzavretých pozícií
            try:
                c = inst['contract']
                if not c:
                    continue

                positions = ib.positions()
                still = any(p.contract.conId == c.conId for p in positions)

                if inst['inPosition'] and not still:
                    # Dodatočná kontrola pozície s väčším oneskorením
                    logging.info(f"Position for {inst['symbol']} appears to be closed, performing additional verification")

                    # Počkáme 2 sekundy a znova skontrolujeme pozíciu
                    time.sleep(2)
                    positions_recheck = ib.positions()
                    still_recheck = any(p.contract.conId == c.conId for p in positions_recheck)

                    if still_recheck:
                        logging.warning(f"False alarm: Position for {inst['symbol']} is still open after recheck")
                        continue  # Pozícia je stále otvorená, pokračujeme ďalej

                    # Ak sme sa dostali sem, pozícia je naozaj uzavretá
                    current_time = time.time()
                    time_in_position = 0
                    if 'entry_timestamp' in inst:
                        time_in_position = current_time - inst['entry_timestamp']

                    # Logujeme informácie o uzavretí pozície
                    if time_in_position < 10:  # Menej ako 10 sekúnd
                        logging.warning(f"Position for {inst['symbol']} was closed very quickly (after {time_in_position:.2f} seconds)")
                        # Podrobnejšie logovanie pre diagnostiku
                        logging.warning(f"Diagnostic info for {inst['symbol']}: entry_signal={inst['entry_signal']}, entry_price={inst['entry_price']}")
                        # Skontrolujeme všetky aktuálne pozície
                        try:
                            all_positions = ib.positions()
                            logging.warning(f"Current positions count: {len(all_positions)}")
                            for pos in all_positions:
                                pos_symbol = pos.contract.localSymbol if hasattr(pos.contract, 'localSymbol') else pos.contract.symbol
                                logging.warning(f"  Position: {pos_symbol}, size: {pos.position}, avgCost: {pos.avgCost}")
                        except Exception as pos_err:
                            logging.error(f"Error checking positions: {pos_err}")

                        # Skontrolujeme všetky executions
                        try:
                            fills = ib.fills()
                            logging.warning(f"Recent fills count: {len(fills)}")
                            for fill in fills[:5]:  # Zobrazíme len posledných 5
                                fill_symbol = fill.contract.localSymbol if hasattr(fill.contract, 'localSymbol') else fill.contract.symbol
                                logging.warning(f"  Fill: {fill_symbol}, side: {fill.execution.side}, qty: {fill.execution.shares}, price: {fill.execution.price}, time: {fill.execution.time}")
                        except Exception as fill_err:
                            logging.error(f"Error checking fills: {fill_err}")
                    else:
                        logging.info(f"Position for {inst['symbol']} was closed (after {time_in_position:.2f} seconds)")

                    if c.conId not in last_closed_conids:
                        # Skúsime získať lepšiu cenu uzavretia z executions
                        exit_price = None
                        close_type = "unknown"

                        try:
                            # Získame executions
                            fills = ib.fills()

                            # Hľadáme najnovšiu execution pre tento kontrakt
                            for fill in reversed(fills):  # Ideme od najnovších
                                if fill.contract.conId == c.conId:
                                    # Našli sme execution pre tento kontrakt
                                    if (fill.execution.side == 'BOT' and inst['entry_signal'] == 'SHORT') or \
                                       (fill.execution.side == 'SLD' and inst['entry_signal'] == 'LONG'):
                                        # Toto je uzavretie pozície
                                        exit_price = fill.execution.price
                                        logging.info(f"Found exit price from executions: {exit_price}")
                                        break
                        except Exception as e:
                            logging.error(f"Error getting executions for {inst['symbol']}: {e}")

                        # Ak sme nenašli cenu z executions, použijeme close z poslednej sviečky
                        if not exit_price:
                            exit_price = lc
                            logging.warning(f"Using last bar close as exit price: {exit_price}")

                        # Kontrola, či existujú potrebné kľúče
                        if 'entry_price' not in inst or 'entry_signal' not in inst:
                            logging.warning(f"Missing entry_price or entry_signal for {inst['symbol']}, cannot calculate P/L")
                            pts = 0
                            profit = 0
                        else:
                            pts = (exit_price - inst['entry_price']) if inst['entry_signal'] == 'LONG' else (inst['entry_price'] - exit_price)
                            profit = pts * inst['multiplier'] * QUANTITY

                        # Určíme typ uzavretia (stop-loss alebo trailing stop)
                        if 'trailing_set' in inst and inst['trailing_set']:
                            close_type = "trailing stop"
                        else:
                            # Ak nebol aktivovaný trailing stop, pravdepodobne to bol stop-loss
                            if 'entry_price' in inst and 'entry_signal' in inst:
                                if (inst['entry_signal'] == 'LONG' and exit_price <= inst['entry_price'] - SL_PTS_LONG * 0.9) or \
                                   (inst['entry_signal'] == 'SHORT' and exit_price >= inst['entry_price'] + SL_PTS_SHORT * 0.9):
                                    close_type = "stop-loss"

                        # Formátovanie s vhodným počtom desatinných miest podľa symbolu
                        if inst['symbol'] in ['M6A', 'M6B', 'M6E']:
                            # Pre menové páry používame 4 desatinné miesta
                            msg = f"Closed {inst['entry_signal']} on {inst['symbol']} @ {exit_price:.4f}, P/L {profit:.2f} USD (via {close_type})"
                        else:
                            # Pre ostatné kontrakty používame 2 desatinné miesta
                            msg = f"Closed {inst['entry_signal']} on {inst['symbol']} @ {exit_price:.2f}, P/L {profit:.2f} USD (via {close_type})"

                        logging.info(msg)
                        send_telegram(msg)

                        try:
                            # Kontrola, či existujú všetky potrebné kľúče
                            if 'symbol' in inst and 'entry_signal' in inst and 'entry_price' in inst and 'entry_time' in inst:
                                sl_price = inst['entry_price'] - SL_PTS_LONG if inst['entry_signal'] == 'LONG' else inst['entry_price'] + SL_PTS_SHORT
                                trailing_set = inst.get('trailing_set', False)  # Použijeme get() s predvolenou hodnotou False

                                append_trade_to_csv(
                                    inst['symbol'], inst['entry_signal'], inst['entry_price'], exit_price, profit,
                                    inst['entry_time'], sl_price, trailing_set
                                )
                            else:
                                logging.warning(f"Missing required keys for {inst.get('symbol', 'unknown')}, cannot append trade to CSV")
                        except Exception as e:
                            logging.error(f"Failed to append trade to CSV: {e}")

                        last_closed_conids.add(c.conId)
                    inst['inPosition'] = False
                    inst['trailing_set'] = False
            except Exception as e:
                logging.error(f"Error checking positions for {inst['symbol']}: {e}", exc_info=True)

        wait_for_next_bar(tf_minutes)

if __name__ == '__main__':
    logging.info(f"Starting Camarilla multi-instrument TF={TIMEFRAME}")
    crashed = False
    consecutive_crashes = 0
    max_consecutive_crashes = 5

    while True:
        try:
            main_loop()
            # Ak sme sa dostali sem, znamená to, že main_loop() skončil bez chyby
            # Resetujeme počítadlo pádov
            consecutive_crashes = 0

            if crashed:
                send_telegram("✅ Bot is up and running again.")
                crashed = False
        except Exception as e:
            consecutive_crashes += 1
            msg = f"❌ Bot crashed: {e}"
            logging.error(msg, exc_info=True)

            # Pošleme notifikáciu len ak je to prvý pád alebo ak je to závažná chyba
            if not crashed and not isinstance(e, TimeoutError):
                send_telegram(msg)
                crashed = True

            # Ak máme príliš veľa pádov za sebou, počkáme dlhšie
            wait_time = 10 * consecutive_crashes if consecutive_crashes <= max_consecutive_crashes else 300

            # Ak je to Socket disconnect alebo ConnectionError, skúsime sa odpojiť a počkať dlhšie
            if "Socket disconnect" in str(e) or isinstance(e, ConnectionError):
                logging.warning(f"Connection issue detected. Waiting {wait_time} seconds before reconnecting.")
                send_telegram(f"⚠️ Connection issue detected. Waiting {wait_time} seconds before reconnecting.")

            try:
                if ib.isConnected():
                    ib.disconnect()
                    logging.info("Disconnected from IB Gateway")
            except Exception as disconnect_err:
                logging.error(f"Error disconnecting: {disconnect_err}")

            logging.info(f"Waiting {wait_time} seconds before restarting...")
            time.sleep(wait_time)
